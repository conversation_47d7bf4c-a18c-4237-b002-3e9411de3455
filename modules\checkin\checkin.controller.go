package checkin

import (
	"net/http"
	"time"

	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type CheckinController struct {
}

func (m CheckinController) Pagination(c core.IHTTPContext) error {
	input := &requests.CheckinPaginationRequest{}
	if err := c.Bind(input); err != nil {
		ierr := core.Error{
			Status:  http.StatusBadRequest,
			Code:    "INVALID_PARAMS",
			Message: "Invalid params request",
		}
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	checkinSvc := services.NewCheckinService(c)
	res, ierr := checkinSvc.Pagination(c.GetPageOptions(), &services.CheckinPaginationOptions{
		UserID:    utils.ToPointer(c.GetUser().ID),
		StartDate: input.StartDate,
		EndDate:   input.EndDate,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m CheckinController) Find(c core.IHTTPContext) error {
	checkinSvc := services.NewCheckinService(c)
	checkin, err := checkinSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, checkin)
}

func (m CheckinController) Create(c core.IHTTPContext) error {
	input := &requests.CheckinCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	payload := &services.CheckinCreatePayload{}
	_ = utils.Copy(payload, input)
	for i, _ := range payload.Items {
		datetime, _ := time.Parse(time.DateTime, utils.ToNonPointer(input.Items[i].Date))
		payload.Items[i].Date = &datetime
	}

	payload.UserId = c.GetUser().ID

	checkinSvc := services.NewCheckinService(c)
	checkin, ierr := checkinSvc.Create(payload)
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusCreated, checkin)
}

func (m CheckinController) Delete(c core.IHTTPContext) error {
	checkinSvc := services.NewCheckinService(c)
	err := checkinSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}

func (m CheckinController) OnThisDay(c core.IHTTPContext) error {
	input := &requests.CheckinOnThisDayRequest{}
	if err := c.Bind(input); err != nil {
		ierr := core.Error{
			Status:  http.StatusBadRequest,
			Code:    "INVALID_PARAMS",
			Message: "Invalid params request",
		}
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	checkinSvc := services.NewCheckinService(c)
	res, ierr := checkinSvc.OnThisDay(&services.CheckinPaginationOptions{
		UserID:    input.UserID,
		StartDate: input.Date, // Use the same date for both start and end to get specific date
		EndDate:   input.Date,
		TeamCode:  input.TeamCode,
		Type:      input.Type,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

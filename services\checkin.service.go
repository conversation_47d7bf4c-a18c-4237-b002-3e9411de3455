package services

import (
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ICheckinService interface {
	Create(input *CheckinCreatePayload) ([]models.Checkin, core.IError)
	Find(id string) (*models.Checkin, core.IError)
	Pagination(pageOptions *core.PageOptions, options *CheckinPaginationOptions) (*repository.Pagination[models.Checkin], core.IError)
	OnThisDay(options *CheckinPaginationOptions) ([]models.Checkin, core.IError)
	Delete(id string) core.IError
}

type checkinService struct {
	ctx core.IContext
}

func (s checkinService) Create(input *CheckinCreatePayload) ([]models.Checkin, core.IError) {
	if len(input.Items) == 0 {
		return []models.Checkin{}, nil
	}

	// Get the date from the first item (assuming all items have the same date)
	date := input.Items[0].Date
	var firstCheckinAt *time.Time

	// Find existing checkins for the user on the same date to preserve FirstCheckinAt
	if date != nil {
		dateStr := date.Format("2006-01-02")
		existingCheckins, ierr := repo.Checkin(s.ctx).
			Where("user_id = ? AND DATE(date) = ?", input.UserId, dateStr).
			Order("first_checkin_at ASC").
			FindAll()

		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}

		// If there are existing checkins, use the FirstCheckinAt from the earliest one
		if len(existingCheckins) > 0 {
			firstCheckinAt = existingCheckins[0].FirstCheckinAt
		}
	}

	// If no existing FirstCheckinAt found, use current time
	if firstCheckinAt == nil {
		firstCheckinAt = utils.GetCurrentDateTime()
	}

	// Mark existing checkins as unused before creating new ones
	ierr := s.updateUnusedByUserAndDate(input.UserId, date)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create new checkin records
	checkins := []models.Checkin{}
	for _, item := range input.Items {
		checkin := models.Checkin{
			BaseModelHardDelete: models.NewBaseModelHardDelete(),
			UserId:              input.UserId,
			Type:                models.CheckinType(item.Type),
			Period:              models.CheckinPeriod(item.Period),
			Location:            item.Location,
			Remarks:             item.Remarks,
			IsUnused:            item.IsUnused,
			Date:                item.Date,
			FirstCheckinAt:      firstCheckinAt,
		}

		// Set LeaveType if provided
		if item.LeaveType != nil {
			leaveType := models.CheckinLeaveType(*item.LeaveType)
			checkin.LeaveType = &leaveType
		}

		checkins = append(checkins, checkin)
	}

	// Create the new checkins in the database
	ierr = repo.Checkin(s.ctx).Create(checkins)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return checkins, nil
}

func (s checkinService) Find(id string) (*models.Checkin, core.IError) {
	return repo.Checkin(s.ctx, repo.CheckinWithAllRelation()).FindOne("id = ?", id)
}

func (s checkinService) Pagination(pageOptions *core.PageOptions, options *CheckinPaginationOptions) (*repository.Pagination[models.Checkin], core.IError) {
	return repo.Checkin(
		s.ctx,
		repo.CheckinWithAllRelation(),
		repo.CheckinWithUser(options.UserID),
		repo.CheckinWithDateRange(options.StartDate, options.EndDate),
		repo.CheckinWithTeamCode(options.TeamCode),
		repo.CheckinWithType(options.Type),
		repo.CheckinOrderBy(pageOptions)).
		Pagination(pageOptions)
}

func (s checkinService) OnThisDay(options *CheckinPaginationOptions) ([]models.Checkin, core.IError) {
	return repo.Checkin(
		s.ctx,
		repo.CheckinWithAllRelation(),
		repo.CheckinWithUser(options.UserID),
		repo.CheckinWithDateRange(options.StartDate, options.EndDate),
		repo.CheckinWithTeamCode(options.TeamCode),
		repo.CheckinWithType(options.Type)).
		Order("created_at DESC").
		FindAll()
}

func (s checkinService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Checkin(s.ctx).Delete("id = ?", id)
}

// Helper method to find existing checkin by user ID and date
func (s checkinService) updateUnusedByUserAndDate(userId string, date *time.Time) core.IError {
	if date == nil {
		return nil
	}

	// Format date to compare only the date part (without time)
	dateStr := date.Format("2006-01-02")
	return repo.Checkin(s.ctx).Where("user_id = ? AND DATE(date) = ?", userId, dateStr).Updates(map[string]interface{}{
		"is_unused": true,
	})
}

func NewCheckinService(ctx core.IContext) ICheckinService {
	return &checkinService{ctx: ctx}
}
